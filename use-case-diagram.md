# Use Case Diagram - Hair Salon Management System

```mermaid
graph TB
    %% Actors
    Admin[🔧 System Admin<br/>ผู้ดูแลระบบ]
    ShopStaff[🧑‍🔧 Shop Staff<br/>ร้าน/พนักงาน]
    Customer[👤 Customer<br/>ลูกค้า]
    
    %% System Boundary
    subgraph System["🎯 Hair Salon Management System"]
        %% Admin Use Cases
        subgraph AdminUC["📊 Admin Functions"]
            UC1[จัดการข้อมูลร้าน<br/>Manage Shop Data]
            UC2[จัดการบัญชีสมาชิก<br/>Manage User Accounts]
            UC3[ตรวจสอบการใช้งาน<br/>Monitor System Usage]
            UC4[ดูรายงานยอดขาย<br/>View Sales Reports]
            UC5[วิเคราะห์สถิติการจอง<br/>Analyze Booking Statistics]
            UC6[จัดการสิทธิ์ผู้ใช้<br/>Manage User Permissions]
            UC7[สำรองข้อมูล<br/>Backup Data]
            UC8[ตรวจสอบระบบ Face Recognition<br/>Monitor Face Recognition]
        end
        
        %% Shop Staff Use Cases
        subgraph StaffUC["💼 Shop Staff Functions"]
            UC9[จัดการรายการบริการ<br/>Manage Services]
            UC10[จัดการสินค้าและโปรโมชั่น<br/>Manage Products & Promotions]
            UC11[จัดการคิวลูกค้า<br/>Manage Customer Queue]
            UC12[ดูข้อมูลลูกค้า<br/>View Customer Data]
            UC13[ตรวจสอบสต็อกสินค้า<br/>Check Inventory]
            UC14[ใช้ระบบตรวจจับใบหน้า<br/>Use Face Recognition]
            UC15[ตอบแชทลูกค้า<br/>Chat with Customers]
            UC16[ให้คะแนนลูกค้า<br/>Rate Customers]
        end
        
        %% Customer Use Cases
        subgraph CustomerUC["✨ Customer Functions"]
            UC17[ค้นหาร้าน<br/>Search Salons]
            UC18[จองคิวล่วงหน้า<br/>Book Appointment]
            UC19[ใช้ AI แนะนำทรงผม<br/>Get AI Hair Recommendations]
            UC20[ดูรายการสินค้า/บริการ<br/>View Products/Services]
            UC21[ตรวจสอบสถานะคิว<br/>Check Queue Status]
            UC22[ชำระเงินออนไลน์<br/>Make Online Payment]
            UC23[ให้รีวิวร้าน/ช่าง<br/>Review Salon/Stylist]
            UC24[รับการแจ้งเตือน<br/>Receive Notifications]
            UC25[จัดการโปรไฟล์<br/>Manage Profile]
        end
        
        %% Shared Use Cases
        subgraph SharedUC["🔄 Shared Functions"]
            UC26[เข้าสู่ระบบ<br/>Login]
            UC27[ออกจากระบบ<br/>Logout]
            UC28[เปลี่ยนรหัสผ่าน<br/>Change Password]
        end
    end
    
    %% External Systems
    PaymentGateway[💳 Payment Gateway]
    GoogleMaps[🗺️ Google Maps API]
    FaceRecognition[👁️ Face Recognition API]
    AIEngine[🤖 AI Recommendation Engine]
    NotificationService[🔔 Notification Service]
    
    %% Admin Relationships
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC6
    Admin --> UC7
    Admin --> UC8
    Admin --> UC26
    Admin --> UC27
    Admin --> UC28
    
    %% Shop Staff Relationships
    ShopStaff --> UC9
    ShopStaff --> UC10
    ShopStaff --> UC11
    ShopStaff --> UC12
    ShopStaff --> UC13
    ShopStaff --> UC14
    ShopStaff --> UC15
    ShopStaff --> UC16
    ShopStaff --> UC26
    ShopStaff --> UC27
    ShopStaff --> UC28
    
    %% Customer Relationships
    Customer --> UC17
    Customer --> UC18
    Customer --> UC19
    Customer --> UC20
    Customer --> UC21
    Customer --> UC22
    Customer --> UC23
    Customer --> UC24
    Customer --> UC25
    Customer --> UC26
    Customer --> UC27
    Customer --> UC28
    
    %% External System Relationships
    UC22 -.-> PaymentGateway
    UC17 -.-> GoogleMaps
    UC14 -.-> FaceRecognition
    UC19 -.-> AIEngine
    UC24 -.-> NotificationService
    
    %% Include/Extend Relationships
    UC18 -.->|includes| UC26
    UC11 -.->|includes| UC14
    UC22 -.->|extends| UC18
    UC24 -.->|extends| UC18
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#01579b
    classDef adminUC fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
    classDef staffUC fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#388e3c
    classDef customerUC fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
    classDef sharedUC fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#7b1fa2
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#d32f2f
    
    class Admin,ShopStaff,Customer actor
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8 adminUC
    class UC9,UC10,UC11,UC12,UC13,UC14,UC15,UC16 staffUC
    class UC17,UC18,UC19,UC20,UC21,UC22,UC23,UC24,UC25 customerUC
    class UC26,UC27,UC28 sharedUC
    class PaymentGateway,GoogleMaps,FaceRecognition,AIEngine,NotificationService external
```

## 📋 Use Case รายละเอียด

### 🔧 System Admin Use Cases
| Use Case ID | ชื่อ Use Case | คำอธิบาย |
|-------------|---------------|----------|
| UC1 | จัดการข้อมูลร้าน | เพิ่ม/แก้ไข/ลบ รายละเอียดร้านในระบบ |
| UC2 | จัดการบัญชีสมาชิก | จัดการบัญชีร้าน/พนักงาน/ลูกค้า |
| UC3 | ตรวจสอบการใช้งาน | ตรวจสอบการใช้งานระบบของผู้ใช้แต่ละประเภท |
| UC4 | ดูรายงานยอดขาย | ดูและส่งออกรายงานยอดขายรายวัน/รายเดือน |
| UC5 | วิเคราะห์สถิติการจอง | วิเคราะห์จำนวนการจอง, ช่วงเวลายอดนิยม |
| UC6 | จัดการสิทธิ์ผู้ใช้ | จัดการสิทธิ์การเข้าถึงของผู้ใช้ในระบบ |
| UC7 | สำรองข้อมูล | สำรองและกู้คืนข้อมูลระบบ |
| UC8 | ตรวจสอบระบบ Face Recognition | ตรวจสอบความถูกต้องและความปลอดภัย |

### 🧑‍🔧 Shop Staff Use Cases
| Use Case ID | ชื่อ Use Case | คำอธิบาย |
|-------------|---------------|----------|
| UC9 | จัดการรายการบริการ | เพิ่ม/แก้ไข/ลบ บริการต่างๆ |
| UC10 | จัดการสินค้าและโปรโมชั่น | จัดการสินค้าและโปรโมชั่นของร้าน |
| UC11 | จัดการคิวลูกค้า | ยืนยัน/ยกเลิก/แก้ไขเวลานัด |
| UC12 | ดูข้อมูลลูกค้า | ดูประวัติการใช้บริการและความชอบ |
| UC13 | ตรวจสอบสต็อกสินค้า | ตรวจสอบสินค้าคงเหลือและแจ้งเตือน |
| UC14 | ใช้ระบบตรวจจับใบหน้า | ตรวจสอบตัวตนและเช็กอินลูกค้า |
| UC15 | ตอบแชทลูกค้า | ตอบคำถามและสื่อสารกับลูกค้า |
| UC16 | ให้คะแนนลูกค้า | ให้คะแนนและรีวิวลูกค้า |

### 👤 Customer Use Cases
| Use Case ID | ชื่อ Use Case | คำอธิบาย |
|-------------|---------------|----------|
| UC17 | ค้นหาร้าน | ค้นหาร้านในพื้นที่หรือตามบริการเฉพาะ |
| UC18 | จองคิวล่วงหน้า | เลือกเวลา, ช่าง, บริการ |
| UC19 | ใช้ AI แนะนำทรงผม | รับคำแนะนำทรงผมจาก AI |
| UC20 | ดูรายการสินค้า/บริการ | ดูรายการสินค้าและบริการของร้าน |
| UC21 | ตรวจสอบสถานะคิว | ตรวจสอบสถานะคิวแบบเรียลไทม์ |
| UC22 | ชำระเงินออนไลน์ | ชำระเงินผ่าน QR Code, PromptPay, บัตรเครดิต |
| UC23 | ให้รีวิวร้าน/ช่าง | ให้คะแนนและรีวิวร้านหรือช่าง |
| UC24 | รับการแจ้งเตือน | รับแจ้งเตือนวันนัด, โปรโมชั่น, คิวใกล้ถึง |
| UC25 | จัดการโปรไฟล์ | แก้ไขข้อมูลส่วนตัวและประวัติการใช้งาน |

### 🔄 Shared Use Cases
| Use Case ID | ชื่อ Use Case | คำอธิบาย |
|-------------|---------------|----------|
| UC26 | เข้าสู่ระบบ | เข้าสู่ระบบด้วย username/password |
| UC27 | ออกจากระบบ | ออกจากระบบอย่างปลอดภัย |
| UC28 | เปลี่ยนรหัสผ่าน | เปลี่ยนรหัสผ่านเพื่อความปลอดภัย |

## 🔗 ความสัมพันธ์ระหว่าง Use Cases

### Include Relationships
- **UC18 (จองคิวล่วงหน้า)** includes **UC26 (เข้าสู่ระบบ)** - ต้องเข้าสู่ระบบก่อนจอง
- **UC11 (จัดการคิวลูกค้า)** includes **UC14 (ใช้ระบบตรวจจับใบหน้า)** - ใช้ Face Recognition ในการจัดการคิว

### Extend Relationships
- **UC22 (ชำระเงินออนไลน์)** extends **UC18 (จองคิวล่วงหน้า)** - สามารถชำระเงินหลังจากจองได้
- **UC24 (รับการแจ้งเตือน)** extends **UC18 (จองคิวล่วงหน้า)** - รับแจ้งเตือนหลังจากจองสำเร็จ

### External System Dependencies
- **UC22** ↔ **Payment Gateway** - ประมวลผลการชำระเงิน
- **UC17** ↔ **Google Maps API** - ค้นหาตำแหน่งร้าน
- **UC14** ↔ **Face Recognition API** - ตรวจจับและยืนยันใบหน้า
- **UC19** ↔ **AI Recommendation Engine** - วิเคราะห์และแนะนำทรงผม
- **UC24** ↔ **Notification Service** - ส่งการแจ้งเตือนต่างๆ
