# Use Case Diagram - Hair Salon Management System (A4 Compact)

```mermaid
graph LR
    %% Actors
    Admin[🔧 Admin]
    Staff[🧑‍🔧 Staff]
    Customer[👤 Customer]

    %% System Boundary
    subgraph System["🎯 Hair Salon Management System"]
        %% Admin Use Cases
        subgraph AdminUC["📊 Admin"]
            A1[จัดการร้าน]
            A2[จัดการผู้ใช้]
            A3[ดูรายงาน]
            A4[สำรองข้อมูล]
        end

        %% Staff Use Cases
        subgraph StaffUC["💼 Staff"]
            S1[จัดการบริการ]
            S2[จัดการคิว]
            S3[ดูข้อมูลลูกค้า]
            S4[Face Recognition]
            S5[แชทลูกค้า]
        end

        %% Customer Use Cases
        subgraph CustomerUC["✨ Customer"]
            C1[ค้นหาร้าน]
            C2[จองคิว]
            C3[AI แนะนำ]
            C4[ชำระเงิน]
            C5[ตรวจสอบคิว]
            C6[รีวิว]
        end

        %% Shared
        Login[🔑 Login]
    end

    %% External Systems
    Payment[💳 Payment]
    Maps[🗺️ Maps]
    AI[🤖 AI Engine]

    %% Relationships
    Admin --> A1
    Admin --> A2
    Admin --> A3
    Admin --> A4
    Admin --> Login

    Staff --> S1
    Staff --> S2
    Staff --> S3
    Staff --> S4
    Staff --> S5
    Staff --> Login

    Customer --> C1
    Customer --> C2
    Customer --> C3
    Customer --> C4
    Customer --> C5
    Customer --> C6
    Customer --> Login

    %% External connections
    C4 -.-> Payment
    C1 -.-> Maps
    S4 -.-> AI
    C3 -.-> AI

    %% Include/Extend
    C2 -.->|includes| Login
    S2 -.->|includes| S4

    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef admin fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef staff fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef customer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef shared fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px

    class Admin,Staff,Customer actor
    class A1,A2,A3,A4 admin
    class S1,S2,S3,S4,S5 staff
    class C1,C2,C3,C4,C5,C6 customer
    class Login shared
    class Payment,Maps,AI external
```

## 📋 Use Case รายละเอียด (กระชับ)

### 🔧 Admin Functions
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| A1 | จัดการร้าน | เพิ่ม/แก้ไข/ลบ ข้อมูลร้าน |
| A2 | จัดการผู้ใช้ | จัดการบัญชีและสิทธิ์ |
| A3 | ดูรายงาน | รายงานยอดขาย/สถิติ |
| A4 | สำรองข้อมูล | Backup/Restore ระบบ |

### 🧑‍🔧 Staff Functions
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| S1 | จัดการบริการ | เพิ่ม/แก้ไข บริการและโปรโมชั่น |
| S2 | จัดการคิว | ยืนยัน/ยกเลิก/แก้ไข การนัดหมาย |
| S3 | ดูข้อมูลลูกค้า | ประวัติ/ความชอบ/สต็อก |
| S4 | Face Recognition | ตรวจสอบตัวตน/เช็กอิน |
| S5 | แชทลูกค้า | ตอบคำถาม/ให้คะแนน |

### 👤 Customer Functions
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| C1 | ค้นหาร้าน | ค้นหาตามพื้นที่/บริการ |
| C2 | จองคิว | เลือกเวลา/ช่าง/บริการ |
| C3 | AI แนะนำ | แนะนำทรงผมด้วย AI |
| C4 | ชำระเงิน | QR/PromptPay/บัตรเครดิต |
| C5 | ตรวจสอบคิว | สถานะคิวเรียลไทม์ |
| C6 | รีวิว | ให้คะแนน/รีวิว/จัดการโปรไฟล์ |

### 🔑 Shared Function
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| Login | เข้าสู่ระบบ | Authentication ทุกผู้ใช้ |

## 🔗 ความสัมพันธ์หลัก

**Include:** C2 (จองคิว) → Login, S2 (จัดการคิว) → S4 (Face Recognition)

**External:** C4 ↔ Payment, C1 ↔ Maps, S4/C3 ↔ AI Engine
