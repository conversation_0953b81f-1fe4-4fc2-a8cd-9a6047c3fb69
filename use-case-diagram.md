# Use Case Diagram - Hair Salon Management System

```mermaid
graph TB
    %% Actors
    Admin[Admin<br/>เจ้าของร้าน]
    Staff[Staff<br/>เสมียน]
    Member[Member<br/>สมาชิก/ลูกค้า]

    %% System Boundary
    subgraph System["Hair Salon Management System"]
        %% Admin Use Cases
        subgraph AdminUC["Admin Functions"]
            UC1[จัดการข้อมูลผู้ใช้งาน]
            UC2[จัดการข้อมูลสินค้า]
            UC3[จัดการสิทธิ์ผู้ใช้]
            UC4[กำหนดโปรโมชั่น]
            UC5[ดูรายงานยอดขาย]
        end

        %% Staff Use Cases
        subgraph StaffUC["Staff Functions"]
            UC6[จัดการข้อมูลสินค้า]
            UC7[ตรวจสอบสต็อก]
            UC8[บันทึกข้อมูลงาน]
            UC9[บันทึกการขาย]
            UC10[จัดการข้อมูลสมาชิก]
            UC11[จัดการแต้มสะสม]
            UC12[ดูประวัติการขาย]
            UC13[ออกใบเสร็จ]
        end

        %% Member Use Cases
        subgraph MemberUC["Member Functions"]
            UC14[ใช้แต้มเป็นส่วนลด]
            UC15[สอบถามแต้มสะสม]
            UC16[ชำระเงินผ่าน QR Code]
        end

        %% Shared Use Cases
        UC17[เข้าสู่ระบบ]
        UC18[ออกจากระบบ]
    end

    %% External Systems
    PaymentSystem[Payment System]
    InventoryAlert[Inventory Alert System]

    %% Admin Relationships
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC17
    Admin --> UC18

    %% Staff Relationships
    Staff --> UC6
    Staff --> UC7
    Staff --> UC8
    Staff --> UC9
    Staff --> UC10
    Staff --> UC11
    Staff --> UC12
    Staff --> UC13
    Staff --> UC17
    Staff --> UC18

    %% Member Relationships
    Member --> UC14
    Member --> UC15
    Member --> UC16

    %% External System Relationships
    UC16 -.-> PaymentSystem
    UC7 -.-> InventoryAlert
    UC13 -.-> PaymentSystem

    %% Include Relationships
    UC1 -.->|includes| UC17
    UC9 -.->|includes| UC13
    UC14 -.->|includes| UC15

    %% Styling
    classDef actor fill:#f0f8ff,stroke:#4682b4,stroke-width:2px
    classDef adminUC fill:#fff8dc,stroke:#daa520,stroke-width:2px
    classDef staffUC fill:#f0fff0,stroke:#32cd32,stroke-width:2px
    classDef memberUC fill:#f5f5dc,stroke:#8b4513,stroke-width:2px
    classDef shared fill:#e6e6fa,stroke:#9370db,stroke-width:2px
    classDef external fill:#ffe4e1,stroke:#dc143c,stroke-width:2px

    class Admin,Staff,Member actor
    class UC1,UC2,UC3,UC4,UC5 adminUC
    class UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13 staffUC
    class UC14,UC15,UC16 memberUC
    class UC17,UC18 shared
    class PaymentSystem,InventoryAlert external
```

## Use Case รายละเอียด

### Admin Functions (เจ้าของร้าน)
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| UC1 | จัดการข้อมูลผู้ใช้งาน | เพิ่ม ลบ แก้ไข ข้อมูลพนักงานและสมาชิก |
| UC2 | จัดการข้อมูลสินค้า | เพิ่ม แก้ไข หรือลบข้อมูลสินค้าในคลัง |
| UC3 | จัดการสิทธิ์ผู้ใช้ | จัดการสิทธิ์และบทบาทของผู้ใช้งานในระบบ |
| UC4 | กำหนดโปรโมชั่น | กำหนดโปรโมชั่น หรือส่วนลดพิเศษ |
| UC5 | ดูรายงานยอดขาย | ดูรายงานยอดขายรายวัน/เดือน/ปี |

### Staff Functions (เสมียน)
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| UC6 | จัดการข้อมูลสินค้า | เพิ่ม แก้ไข หรือลบข้อมูลสินค้าในคลัง |
| UC7 | ตรวจสอบสต็อก | ตรวจสอบระดับสต็อก และรับการแจ้งเตือนเมื่อวัสดุใกล้หมด |
| UC8 | บันทึกข้อมูลงาน | บันทึกข้อมูลงาน (รายละเอียด, ราคา, วันที่นัดรับ) |
| UC9 | บันทึกการขาย | บันทึกการขายและออกใบเสร็จ |
| UC10 | จัดการข้อมูลสมาชิก | เพิ่มลบและแก้ไขข้อมูลสมาชิก (ชื่อ, เบอร์โทร, รหัสสมาชิก) |
| UC11 | จัดการแต้มสะสม | จัดการแต้มสะสมของสมาชิก |
| UC12 | ดูประวัติการขาย | ดูประวัติการขาย และตรวจสอบการชำระเงินของลูกค้า |
| UC13 | ออกใบเสร็จ | ออกใบเสร็จพร้อม QR Code สำหรับชำระเงิน |

### Member Functions (สมาชิก/ลูกค้า)
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| UC14 | ใช้แต้มเป็นส่วนลด | ใช้แต้มเพื่อเป็นส่วนลดตามเงื่อนไข |
| UC15 | สอบถามแต้มสะสม | สอบถามแต้มจากพนักงาน หรือดูในใบเสร็จ |
| UC16 | ชำระเงินผ่าน QR Code | ชำระเงินผ่านคิวอาร์โค้ดในใบเสร็จ |

### Shared Functions
| ID | Use Case | รายละเอียด |
|----|----------|------------|
| UC17 | เข้าสู่ระบบ | เข้าสู่ระบบด้วย username/password |
| UC18 | ออกจากระบบ | ออกจากระบบอย่างปลอดภัย |

## ความสัมพันธ์ระหว่าง Use Cases

### Include Relationships
- UC1 (จัดการข้อมูลผู้ใช้งาน) includes UC17 (เข้าสู่ระบบ)
- UC9 (บันทึกการขาย) includes UC13 (ออกใบเสร็จ)
- UC14 (ใช้แต้มเป็นส่วนลด) includes UC15 (สอบถามแต้มสะสม)

### External System Dependencies
- UC16 (ชำระเงินผ่าน QR Code) ↔ Payment System
- UC7 (ตรวจสอบสต็อก) ↔ Inventory Alert System
- UC13 (ออกใบเสร็จ) ↔ Payment System
