graph LR
    subgraph "Slide 1: Problem Statement"
        Problem1[❌ Long waiting times]
        Problem2[❌ Manual booking process]
        Problem3[❌ Poor customer experience]
        Problem4[❌ Limited shop visibility]
    end
    
    subgraph "Slide 2: Our Solution"
        Solution[🎯 Hair Salon Management System<br/>ระบบ<|im_start|>ร้าน<|im_start|>ผมครบวงจร]
    end
    
    subgraph "Slide 3: Key Features"
        Feature1[🔍 Smart Shop Discovery]
        Feature2[📅 Online Booking System]
        Feature3[🤖 AI Hair Recommendation]
        Feature4[📷 Face Recognition Check-in]
        Feature5[💳 Digital Payment Integration]
        Feature6[📊 Real-time Analytics]
    end
    
    subgraph "Slide 4: Target Users"
        User1[👤 Customers<br/>25-45 years old<br/>Tech-savvy]
        User2[🏪 Shop Owners<br/>Small to medium salons<br/>Want to digitize]
        User3[👨‍💼 Platform Admin<br/>System management<br/>Business analytics]
    end
    
    subgraph "Slide 5: Business Impact"
        Impact1[📈 30% Revenue Increase]
        Impact2[⏰ 50% Reduced Wait Time]
        Impact3[⭐ 40% Better Customer Satisfaction]
        Impact4[💰 25% Cost Reduction]
    end
    
    Problem1 --> Solution
    Problem2 --> Solution
    Problem3 --> Solution
    Problem4 --> Solution
    
    Solution --> Feature1
    Solution --> Feature2
    Solution --> Feature3
    Solution --> Feature4
    Solution --> Feature5
    Solution --> Feature6
    
    Feature1 --> User1
    Feature2 --> User2
    Feature6 --> User3
    
    User1 --> Impact3
    User2 --> Impact1
    User3 --> Impact4
    
    classDef problemStyle fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#d32f2f
    classDef solutionStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:4px,color:#388e3c,font-weight:bold
    classDef featureStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#1976d2
    classDef userStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#f57c00
    classDef impactStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#7b1fa2,font-weight:bold
    
    class Problem1,Problem2,Problem3,Problem4 problemStyle
    class Solution solutionStyle
    class Feature1,Feature2,Feature3,Feature4,Feature5,Feature6 featureStyle
    class User1,User2,User3 userStyle
    class Impact1,Impact2,Impact3,Impact4 impactStyle