<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Use Case Diagram - Hair Salon Management System</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        @page {
            size: A4;
            margin: 1.5cm;
        }
        
        body {
            font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 15px;
            background: white;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #4682b4;
            padding-bottom: 8px;
        }
        
        .header h1 {
            color: #4682b4;
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 12px;
        }
        
        .diagram-container {
            text-align: center;
            margin: 20px 0;
            height: 80vh;
        }

        .mermaid {
            height: 100%;
        }
        
        @media print {
            body { margin: 0; padding: 10px; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Hair Salon Management System</h1>
        <p>Use Case Diagram - ระบบจัดการร้านทำผมครบวงจร</p>
    </div>

    <div class="diagram-container">
        <div class="mermaid">
graph LR
    %% Actors
    Admin[Admin<br/>เจ้าของร้าน]
    Staff[Staff<br/>เสมียน]
    Member[Member<br/>สมาชิก/ลูกค้า]
    
    %% System Boundary
    subgraph System["Hair Salon Management System"]
        %% Admin Use Cases
        subgraph AdminUC["Admin Functions"]
            UC1[จัดการข้อมูลผู้ใช้งาน]
            UC2[จัดการข้อมูลสินค้า]
            UC3[จัดการสิทธิ์ผู้ใช้]
            UC4[กำหนดโปรโมชั่น]
            UC5[ดูรายงานยอดขาย]
        end
        
        %% Staff Use Cases
        subgraph StaffUC["Staff Functions"]
            UC6[จัดการข้อมูลสินค้า]
            UC7[ตรวจสอบสต็อก]
            UC8[บันทึกข้อมูลงาน]
            UC9[บันทึกการขาย]
            UC10[จัดการข้อมูลสมาชิก]
            UC11[จัดการแต้มสะสม]
            UC12[ดูประวัติการขาย]
            UC13[ออกใบเสร็จ]
        end
        
        %% Member Use Cases
        subgraph MemberUC["Member Functions"]
            UC14[ใช้แต้มเป็นส่วนลด]
            UC15[สอบถามแต้มสะสม]
            UC16[ชำระเงินผ่าน QR Code]
        end
        
        %% Shared Use Cases
        UC17[เข้าสู่ระบบ]
        UC18[ออกจากระบบ]
    end
    
    %% External Systems
    PaymentSystem[Payment System]
    InventoryAlert[Inventory Alert System]
    
    %% Admin Relationships
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC17
    Admin --> UC18
    
    %% Staff Relationships
    Staff --> UC6
    Staff --> UC7
    Staff --> UC8
    Staff --> UC9
    Staff --> UC10
    Staff --> UC11
    Staff --> UC12
    Staff --> UC13
    Staff --> UC17
    Staff --> UC18
    
    %% Member Relationships
    Member --> UC14
    Member --> UC15
    Member --> UC16
    
    %% External System Relationships
    UC16 -.-> PaymentSystem
    UC7 -.-> InventoryAlert
    UC13 -.-> PaymentSystem
    
    %% Include Relationships
    UC1 -.->|includes| UC17
    UC9 -.->|includes| UC13
    UC14 -.->|includes| UC15
    
    %% Styling
    classDef actor fill:#f0f8ff,stroke:#4682b4,stroke-width:2px
    classDef adminUC fill:#fff8dc,stroke:#daa520,stroke-width:2px
    classDef staffUC fill:#f0fff0,stroke:#32cd32,stroke-width:2px
    classDef memberUC fill:#f5f5dc,stroke:#8b4513,stroke-width:2px
    classDef shared fill:#e6e6fa,stroke:#9370db,stroke-width:2px
    classDef external fill:#ffe4e1,stroke:#dc143c,stroke-width:2px
    
    class Admin,Staff,Member actor
    class UC1,UC2,UC3,UC4,UC5 adminUC
    class UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13 staffUC
    class UC14,UC15,UC16 memberUC
    class UC17,UC18 shared
    class PaymentSystem,InventoryAlert external
        </div>
    </div>



    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
