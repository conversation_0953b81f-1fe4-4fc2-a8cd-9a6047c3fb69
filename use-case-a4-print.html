<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Use Case Diagram - Hair Salon Management System</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        @page {
            size: A4;
            margin: 1.5cm;
        }
        
        body {
            font-family: 'Sarabun', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 15px;
            background: white;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #4682b4;
            padding-bottom: 8px;
        }
        
        .header h1 {
            color: #4682b4;
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }
        
        .header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 12px;
        }
        
        .diagram-container {
            text-align: center;
            margin: 15px 0;
        }
        
        .summary {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
            font-size: 11px;
        }
        
        .summary-section {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
        }
        
        .summary-section h3 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 12px;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
            font-weight: bold;
        }
        
        .use-case-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .use-case-list li {
            padding: 2px 0;
            border-bottom: 1px dotted #eee;
            font-size: 10px;
        }
        
        .use-case-list li:last-child {
            border-bottom: none;
        }
        
        .admin { color: #daa520; }
        .staff { color: #32cd32; }
        .member { color: #8b4513; }
        .shared { color: #9370db; }
        .external { color: #dc143c; }
        
        @media print {
            body { margin: 0; padding: 10px; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Hair Salon Management System</h1>
        <p>Use Case Diagram - ระบบจัดการร้านทำผมครบวงจร</p>
    </div>

    <div class="diagram-container">
        <div class="mermaid">
graph TB
    %% Actors
    Admin[Admin<br/>เจ้าของร้าน]
    Staff[Staff<br/>เสมียน]
    Member[Member<br/>สมาชิก/ลูกค้า]
    
    %% System Boundary
    subgraph System["Hair Salon Management System"]
        %% Admin Use Cases
        subgraph AdminUC["Admin Functions"]
            UC1[จัดการข้อมูลผู้ใช้งาน]
            UC2[จัดการข้อมูลสินค้า]
            UC3[จัดการสิทธิ์ผู้ใช้]
            UC4[กำหนดโปรโมชั่น]
            UC5[ดูรายงานยอดขาย]
        end
        
        %% Staff Use Cases
        subgraph StaffUC["Staff Functions"]
            UC6[จัดการข้อมูลสินค้า]
            UC7[ตรวจสอบสต็อก]
            UC8[บันทึกข้อมูลงาน]
            UC9[บันทึกการขาย]
            UC10[จัดการข้อมูลสมาชิก]
            UC11[จัดการแต้มสะสม]
            UC12[ดูประวัติการขาย]
            UC13[ออกใบเสร็จ]
        end
        
        %% Member Use Cases
        subgraph MemberUC["Member Functions"]
            UC14[ใช้แต้มเป็นส่วนลด]
            UC15[สอบถามแต้มสะสม]
            UC16[ชำระเงินผ่าน QR Code]
        end
        
        %% Shared Use Cases
        UC17[เข้าสู่ระบบ]
        UC18[ออกจากระบบ]
    end
    
    %% External Systems
    PaymentSystem[Payment System]
    InventoryAlert[Inventory Alert System]
    
    %% Admin Relationships
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC17
    Admin --> UC18
    
    %% Staff Relationships
    Staff --> UC6
    Staff --> UC7
    Staff --> UC8
    Staff --> UC9
    Staff --> UC10
    Staff --> UC11
    Staff --> UC12
    Staff --> UC13
    Staff --> UC17
    Staff --> UC18
    
    %% Member Relationships
    Member --> UC14
    Member --> UC15
    Member --> UC16
    
    %% External System Relationships
    UC16 -.-> PaymentSystem
    UC7 -.-> InventoryAlert
    UC13 -.-> PaymentSystem
    
    %% Include Relationships
    UC1 -.->|includes| UC17
    UC9 -.->|includes| UC13
    UC14 -.->|includes| UC15
    
    %% Styling
    classDef actor fill:#f0f8ff,stroke:#4682b4,stroke-width:2px
    classDef adminUC fill:#fff8dc,stroke:#daa520,stroke-width:2px
    classDef staffUC fill:#f0fff0,stroke:#32cd32,stroke-width:2px
    classDef memberUC fill:#f5f5dc,stroke:#8b4513,stroke-width:2px
    classDef shared fill:#e6e6fa,stroke:#9370db,stroke-width:2px
    classDef external fill:#ffe4e1,stroke:#dc143c,stroke-width:2px
    
    class Admin,Staff,Member actor
    class UC1,UC2,UC3,UC4,UC5 adminUC
    class UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13 staffUC
    class UC14,UC15,UC16 memberUC
    class UC17,UC18 shared
    class PaymentSystem,InventoryAlert external
        </div>
    </div>

    <div class="summary">
        <div class="summary-section">
            <h3 class="admin">Admin Functions (เจ้าของร้าน)</h3>
            <ul class="use-case-list">
                <li><strong>UC1:</strong> จัดการข้อมูลผู้ใช้งาน - เพิ่ม ลบ แก้ไข พนักงานและสมาชิก</li>
                <li><strong>UC2:</strong> จัดการข้อมูลสินค้า - เพิ่ม แก้ไข ลบ สินค้าในคลัง</li>
                <li><strong>UC3:</strong> จัดการสิทธิ์ผู้ใช้ - จัดการสิทธิ์และบทบาท</li>
                <li><strong>UC4:</strong> กำหนดโปรโมชั่น - กำหนดโปรโมชั่นและส่วนลด</li>
                <li><strong>UC5:</strong> ดูรายงานยอดขาย - รายงานรายวัน/เดือน/ปี</li>
            </ul>
            
            <h3 class="shared">Shared Functions</h3>
            <ul class="use-case-list">
                <li><strong>UC17:</strong> เข้าสู่ระบบ - Authentication</li>
                <li><strong>UC18:</strong> ออกจากระบบ - Logout</li>
            </ul>
        </div>
        
        <div class="summary-section">
            <h3 class="staff">Staff Functions (เสมียน)</h3>
            <ul class="use-case-list">
                <li><strong>UC6:</strong> จัดการข้อมูลสินค้า - เพิ่ม แก้ไข ลบ สินค้า</li>
                <li><strong>UC7:</strong> ตรวจสอบสต็อก - ตรวจสอบและแจ้งเตือน</li>
                <li><strong>UC8:</strong> บันทึกข้อมูลงาน - รายละเอียด ราคา วันนัดรับ</li>
                <li><strong>UC9:</strong> บันทึกการขาย - บันทึกการขาย</li>
                <li><strong>UC10:</strong> จัดการข้อมูลสมาชิก - ชื่อ เบอร์ รหัสสมาชิก</li>
                <li><strong>UC11:</strong> จัดการแต้มสะสม - จัดการแต้มสมาชิก</li>
                <li><strong>UC12:</strong> ดูประวัติการขาย - ประวัติและการชำระเงิน</li>
                <li><strong>UC13:</strong> ออกใบเสร็จ - ออกใบเสร็จพร้อม QR Code</li>
            </ul>
        </div>
        
        <div class="summary-section">
            <h3 class="member">Member Functions (สมาชิก/ลูกค้า)</h3>
            <ul class="use-case-list">
                <li><strong>UC14:</strong> ใช้แต้มเป็นส่วนลด - ใช้แต้มตามเงื่อนไข</li>
                <li><strong>UC15:</strong> สอบถามแต้มสะสม - สอบถามแต้มจากพนักงาน</li>
                <li><strong>UC16:</strong> ชำระเงินผ่าน QR Code - ชำระผ่านคิวอาร์โค้ด</li>
            </ul>
            
            <h3 class="external">External Systems</h3>
            <ul class="use-case-list">
                <li><strong>Payment System:</strong> ระบบชำระเงิน</li>
                <li><strong>Inventory Alert:</strong> ระบบแจ้งเตือนสต็อก</li>
            </ul>
            
            <h3>Include Relationships</h3>
            <ul class="use-case-list">
                <li>UC1 → UC17 (ต้องเข้าสู่ระบบ)</li>
                <li>UC9 → UC13 (รวมการออกใบเสร็จ)</li>
                <li>UC14 → UC15 (ตรวจสอบแต้ม)</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
