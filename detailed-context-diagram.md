# Detailed Context Diagram - Hair Salon Management System

```mermaid
C4Context
    title 🏢 HAIR SALON MANAGEMENT SYSTEM - CONTEXT DIAGRAM

    Person(admin, "🔧 ผู้ดูแลระบบ<br/>(SYSTEM ADMIN)", "📊 ควบคุมระบบโดยรวม<br/>• จัดการข้อมูลร้าน<br/>• จัดการบัญชีสมาชิก<br/>• ตรวจสอบการใช้งาน<br/>• ดูรายงานยอดขาย<br/>• วิเคราะห์สถิติการจอง<br/>• จัดการสิทธิ์ผู้ใช้<br/>• สำรองข้อมูล<br/>• ตรวจสอบระบบตรวจจับใบหน้า")

    Person(shopStaff, "🧑‍🔧 ร้าน/พนักงานร้าน<br/>(SHOP STAFF)", "💼 ให้บริการลูกค้า จัดการร้าน<br/>• จัดการรายการบริการ<br/>• จัดการสินค้าและโปรโมชั่น<br/>• จัดการคิวลูกค้า<br/>• ดูข้อมูลลูกค้า<br/>• ตรวจสอบสต็อกสินค้า<br/>• ใช้ระบบตรวจจับใบหน้า<br/>• ตอบแชทลูกค้า<br/>• ให้คะแนนลูกค้า")

    Person(customer, "👤 ลูกค้า<br/>(CUSTOMER)", "✨ ใช้บริการร้านทำผม<br/>• ค้นหาร้านในพื้นที่<br/>• จองคิวล่วงหน้า<br/>• ใช้ AI แนะนำทรงผม<br/>• ดูรายการสินค้า/บริการ<br/>• ตรวจสอบสถานะคิว<br/>• ชำระเงินออนไลน์<br/>• ให้รีวิวร้าน/ช่าง<br/>• รับการแจ้งเตือน<br/>• จัดการโปรไฟล์")

    System(hairSalonSystem, "🎯 HAIR SALON<br/>MANAGEMENT SYSTEM", "🚀 ระบบจัดการร้านทำผมครบวงจร<br/>📱 รองรับการจองออนไลน์<br/>🤖 AI แนะนำทรงผม<br/>⚡ การจัดการธุรกิจแบบเรียลไทม์")

    System_Ext(paymentGateway, "💳 PAYMENT<br/>GATEWAY", "💰 ระบบชำระเงินออนไลน์<br/>📱 QR Code, PromptPay<br/>💳 บัตรเครดิต")
    System_Ext(googleMaps, "🗺️ GOOGLE MAPS<br/>API", "📍 ค้นหาร้านในพื้นที่<br/>🗺️ แสดงแผนที่<br/>🧭 นำทางไปร้าน")
    System_Ext(cloudStorage, "☁️ CLOUD<br/>STORAGE", "📸 เก็บข้อมูลภาพผลงาน<br/>👤 โปรไฟล์ลูกค้า<br/>💾 ข้อมูลสำรอง")
    System_Ext(faceRecognition, "👁️ FACE RECOGNITION<br/>API", "🔍 ระบบตรวจจับใบหน้า<br/>✅ ตรวจสอบตัวตน<br/>⚡ เช็กอินอัตโนมัติ")
    System_Ext(aiRecommendation, "🤖 AI RECOMMENDATION<br/>ENGINE", "🧠 วิเคราะห์ใบหน้า<br/>💇‍♀️ แนะนำทรงผม<br/>⭐ แนะนำตามความนิยม")
    System_Ext(notificationService, "🔔 NOTIFICATION<br/>SERVICE", "📅 แจ้งเตือนวันนัด<br/>🎉 โปรโมชั่นใหม่<br/>⏰ คิวใกล้ถึง")
    System_Ext(chatService, "💬 CHAT/MESSAGE<br/>SERVICE", "💬 ระบบแชทลูกค้า<br/>❓ ตอบคำถาม<br/>⚡ สื่อสารแบบเรียลไทม์")
    System_Ext(analyticsService, "📊 ANALYTICS &<br/>REPORTING", "📈 วิเคราะห์ยอดขาย<br/>📊 สถิติการจอง<br/>📋 รายงานประจำวัน/เดือน")
    System_Ext(backupService, "🔒 BACKUP &<br/>RECOVERY", "💾 สำรองข้อมูลระบบ<br/>🔄 กู้คืนข้อมูล<br/>🛡️ ความปลอดภัยข้อมูล")

    %% Admin Relations
    Rel(admin, hairSalonSystem, "จัดการระบบโดยรวม<br/>ควบคุมข้อมูลและสิทธิ์")
    Rel(hairSalonSystem, analyticsService, "ดึงรายงานและสถิติ")
    Rel(hairSalonSystem, backupService, "สำรองและกู้คืนข้อมูล")
    
    %% Shop Staff Relations
    Rel(shopStaff, hairSalonSystem, "จัดการร้าน ดูแลลูกค้า<br/>ใช้ระบบตรวจจับใบหน้า")
    Rel(hairSalonSystem, faceRecognition, "ตรวจสอบตัวตนลูกค้า")
    Rel(hairSalonSystem, chatService, "ตอบแชทลูกค้า")
    
    %% Customer Relations
    Rel(customer, hairSalonSystem, "ค้นหาร้าน จองคิว<br/>ใช้ AI ชำระเงิน")
    Rel(hairSalonSystem, paymentGateway, "ประมวลผลการชำระเงิน")
    Rel(hairSalonSystem, googleMaps, "ค้นหาร้านและแสดงแผนที่")
    Rel(hairSalonSystem, aiRecommendation, "แนะนำทรงผมด้วย AI")
    Rel(hairSalonSystem, notificationService, "ส่งการแจ้งเตือน")
    
    %% Shared Relations
    Rel(hairSalonSystem, cloudStorage, "เก็บข้อมูลและไฟล์")

    UpdateElementStyle(admin, $fontColor="white", $bgColor="#f57c00")
    UpdateElementStyle(shopStaff, $fontColor="white", $bgColor="#388e3c")
    UpdateElementStyle(customer, $fontColor="white", $bgColor="#1976d2")
    UpdateElementStyle(hairSalonSystem, $fontColor="white", $bgColor="#d32f2f")
    UpdateElementStyle(faceRecognition, $fontColor="white", $bgColor="#7b1fa2")
    UpdateElementStyle(aiRecommendation, $fontColor="white", $bgColor="#7b1fa2")
```

## 📋 รายละเอียดฟังก์ชันของผู้ใช้แต่ละประเภท

### 🔧 ผู้ดูแลระบบ (SYSTEM ADMIN)
**🎯 บทบาทหลัก**: ควบคุมการทำงานของระบบโดยรวมและดูแลความถูกต้องของข้อมูล

**⚙️ ฟังก์ชันหลัก**:
- 🏪 **จัดการข้อมูลร้าน** (เพิ่ม/แก้ไข/ลบ รายละเอียดร้านในระบบ)
- 👥 **จัดการบัญชีสมาชิก** (ร้าน/พนักงาน/ลูกค้า)
- 📊 **ตรวจสอบการใช้งานระบบ** ของผู้ใช้แต่ละประเภท
- 💰 **ดูและส่งออกรายงานยอดขาย** รายวัน/รายเดือน
- 📈 **วิเคราะห์และดูสถิติการจอง** (จำนวนการจอง, ช่วงเวลายอดนิยม)
- 🔐 **จัดการสิทธิ์การเข้าถึง** ของผู้ใช้ในระบบ
- 💾 **สำรองและกู้คืนข้อมูลระบบ** (Database Backup & Restore)
- 👁️ **ตรวจสอบการทำงานของระบบตรวจจับใบหน้า** (ความถูกต้องและความปลอดภัย)

### 🧑‍🔧 ร้าน/พนักงานร้าน (SHOP STAFF)
**🎯 บทบาทหลัก**: รับผิดชอบการให้บริการลูกค้า ดูแลคิวและข้อมูลภายในร้าน

**💼 ฟังก์ชันหลัก**:
- ✂️ **จัดการรายการบริการ** (เพิ่ม/แก้ไข/ลบ ตัดผม ทำสี สระผม ฯลฯ)
- 🎁 **จัดการสินค้าและโปรโมชั่น** ของร้าน
- 📅 **ตรวจสอบและจัดการคิวลูกค้า** (ยืนยัน/ยกเลิก/แก้ไขเวลานัด)
- 👤 **ดูข้อมูลลูกค้า** (ประวัติการใช้บริการ, ความชอบ)
- 📦 **ตรวจสอบสถานะสินค้าในสต็อก** (สินค้าคงเหลือ, แจ้งเตือนเมื่อใกล้หมด)
- 👁️ **ใช้ระบบตรวจจับใบหน้า** เพื่อตรวจสอบตัวตนของลูกค้า (เช็กอินอัตโนมัติ)
- 💬 **ตอบแชทหรือคำถามจากลูกค้า** (ระบบ Message/Chat)
- ⭐ **ให้คะแนน/รีวิวลูกค้า** (กรณีลูกค้าประพฤติตัวไม่เหมาะสม)

### 👤 ลูกค้า (CUSTOMER)
**🎯 บทบาทหลัก**: ผู้ใช้งานระบบเพื่อนัดหมาย เข้ารับบริการ และดูข้อมูลต่างๆ

**✨ ฟังก์ชันหลัก**:
- 🔍 **ค้นหาร้านในพื้นที่** หรือร้านที่มีบริการเฉพาะทาง (ค้นหาจากแผนที่, บริการ, โปรโมชั่น)
- 📅 **จองคิวล่วงหน้า** (เลือกเวลา, ช่าง, บริการ)
- 🤖 **เลือกทรงผมแนะนำจากระบบ AI** (AI แนะนำตามรูปหน้า / ความนิยม)
- 🛍️ **ดูรายการสินค้า/บริการ** ของร้าน
- ⏰ **ตรวจสอบสถานะคิว** ของตนเองแบบเรียลไทม์
- 💳 **ชำระเงินออนไลน์ผ่านระบบ** (QR Code, PromptPay, บัตรเครดิต ฯลฯ)
- ⭐ **ดูและให้รีวิวร้าน/ช่าง**
- 🔔 **รับการแจ้งเตือน** (แจ้งเตือนวันนัด, โปรโมชั่นใหม่, คิวใกล้ถึง)
- 👤 **จัดการข้อมูลโปรไฟล์ส่วนตัว** (แก้ไขข้อมูล, ประวัติการใช้งาน)
