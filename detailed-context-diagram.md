# Detailed Context Diagram - Hair Salon Management System

```mermaid
C4Context
    title Detailed System Context - Hair Salon Management System with User Functions

    Person(admin, "ผู้ดูแลระบบ (Admin)", "ควบคุมระบบโดยรวม<br/>• จัดการข้อมูลร้าน<br/>• จัดการบัญชีสมาชิก<br/>• ตรวจสอบการใช้งาน<br/>• ดูรายงานยอดขาย<br/>• วิเคราะห์สถิติการจอง<br/>• จัดการสิทธิ์ผู้ใช้<br/>• สำรองข้อมูล<br/>• ตรวจสอบระบบตรวจจับใบหน้า")
    
    Person(shopStaff, "ร้าน/พนักงานร้าน", "ให้บริการลูกค้า จัดการร้าน<br/>• จัดการรายการบริการ<br/>• จัดการสินค้าและโปรโมชั่น<br/>• จัดการคิวลูกค้า<br/>• ดูข้อมูลลูกค้า<br/>• ตรวจสอบสต็อกสินค้า<br/>• ใช้ระบบตรวจจับใบหน้า<br/>• ตอบแชทลูกค้า<br/>• ให้คะแนนลูกค้า")
    
    Person(customer, "ลูกค้า (Customer)", "ใช้บริการร้านทำผม<br/>• ค้นหาร้านในพื้นที่<br/>• จองคิวล่วงหน้า<br/>• ใช้ AI แนะนำทรงผม<br/>• ดูรายการสินค้า/บริการ<br/>• ตรวจสอบสถานะคิว<br/>• ชำระเงินออนไลน์<br/>• ให้รีวิวร้าน/ช่าง<br/>• รับการแจ้งเตือน<br/>• จัดการโปรไฟล์")

    System(hairSalonSystem, "Hair Salon Management System", "ระบบจัดการร้านทำผมครบวงจร<br/>รองรับการจองออนไลน์ AI แนะนำทรงผม<br/>และการจัดการธุรกิจแบบเรียลไทม์")

    System_Ext(paymentGateway, "Payment Gateway", "ระบบชำระเงินออนไลน์<br/>QR Code, PromptPay<br/>บัตรเครดิต")
    System_Ext(googleMaps, "Google Maps API", "ค้นหาร้านในพื้นที่<br/>แสดงแผนที่<br/>นำทางไปร้าน")
    System_Ext(cloudStorage, "Cloud Storage", "เก็บข้อมูลภาพผลงาน<br/>โปรไฟล์ลูกค้า<br/>ข้อมูลสำรอง")
    System_Ext(faceRecognition, "Face Recognition API", "ระบบตรวจจับใบหน้า<br/>ตรวจสอบตัวตน<br/>เช็กอินอัตโนมัติ")
    System_Ext(aiRecommendation, "AI Recommendation Engine", "วิเคราะห์ใบหน้า<br/>แนะนำทรงผม<br/>แนะนำตามความนิยม")
    System_Ext(notificationService, "Notification Service", "แจ้งเตือนวันนัด<br/>โปรโมชั่นใหม่<br/>คิวใกล้ถึง")
    System_Ext(chatService, "Chat/Message Service", "ระบบแชทลูกค้า<br/>ตอบคำถาม<br/>สื่อสารแบบเรียลไทม์")
    System_Ext(analyticsService, "Analytics & Reporting", "วิเคราะห์ยอดขาย<br/>สถิติการจอง<br/>รายงานประจำวัน/เดือน")
    System_Ext(backupService, "Backup & Recovery", "สำรองข้อมูลระบบ<br/>กู้คืนข้อมูล<br/>ความปลอดภัยข้อมูล")

    %% Admin Relations
    Rel(admin, hairSalonSystem, "จัดการระบบโดยรวม<br/>ควบคุมข้อมูลและสิทธิ์")
    Rel(hairSalonSystem, analyticsService, "ดึงรายงานและสถิติ")
    Rel(hairSalonSystem, backupService, "สำรองและกู้คืนข้อมูล")
    
    %% Shop Staff Relations
    Rel(shopStaff, hairSalonSystem, "จัดการร้าน ดูแลลูกค้า<br/>ใช้ระบบตรวจจับใบหน้า")
    Rel(hairSalonSystem, faceRecognition, "ตรวจสอบตัวตนลูกค้า")
    Rel(hairSalonSystem, chatService, "ตอบแชทลูกค้า")
    
    %% Customer Relations
    Rel(customer, hairSalonSystem, "ค้นหาร้าน จองคิว<br/>ใช้ AI ชำระเงิน")
    Rel(hairSalonSystem, paymentGateway, "ประมวลผลการชำระเงิน")
    Rel(hairSalonSystem, googleMaps, "ค้นหาร้านและแสดงแผนที่")
    Rel(hairSalonSystem, aiRecommendation, "แนะนำทรงผมด้วย AI")
    Rel(hairSalonSystem, notificationService, "ส่งการแจ้งเตือน")
    
    %% Shared Relations
    Rel(hairSalonSystem, cloudStorage, "เก็บข้อมูลและไฟล์")

    UpdateElementStyle(admin, $fontColor="white", $bgColor="#f57c00")
    UpdateElementStyle(shopStaff, $fontColor="white", $bgColor="#388e3c")
    UpdateElementStyle(customer, $fontColor="white", $bgColor="#1976d2")
    UpdateElementStyle(hairSalonSystem, $fontColor="white", $bgColor="#d32f2f")
    UpdateElementStyle(faceRecognition, $fontColor="white", $bgColor="#7b1fa2")
    UpdateElementStyle(aiRecommendation, $fontColor="white", $bgColor="#7b1fa2")
```

## รายละเอียดฟังก์ชันของผู้ใช้แต่ละประเภท

### 🔧 ผู้ดูแลระบบ (Admin)
**บทบาท**: ควบคุมการทำงานของระบบโดยรวมและดูแลความถูกต้องของข้อมูล

**ฟังก์ชันหลัก**:
- จัดการข้อมูลร้าน (เพิ่ม/แก้ไข/ลบ รายละเอียดร้านในระบบ)
- จัดการบัญชีสมาชิก (ร้าน/พนักงาน/ลูกค้า)
- ตรวจสอบการใช้งานระบบของผู้ใช้แต่ละประเภท
- ดูและส่งออกรายงานยอดขายรายวัน/รายเดือน
- วิเคราะห์และดูสถิติการจอง
- จัดการสิทธิ์การเข้าถึงของผู้ใช้ในระบบ
- สำรองและกู้คืนข้อมูลระบบ
- ตรวจสอบการทำงานของระบบตรวจจับใบหน้า

### 🧑‍🔧 ร้าน/พนักงานร้าน
**บทบาท**: รับผิดชอบการให้บริการลูกค้า ดูแลคิวและข้อมูลภายในร้าน

**ฟังก์ชันหลัก**:
- เพิ่ม/แก้ไข/ลบ รายการบริการ
- จัดการสินค้าและโปรโมชั่นของร้าน
- ตรวจสอบและจัดการคิวลูกค้า
- ดูข้อมูลลูกค้า (ประวัติการใช้บริการ, ความชอบ)
- ตรวจสอบสถานะสินค้าในสต็อก
- ใช้ระบบตรวจจับใบหน้าเพื่อตรวจสอบตัวตนของลูกค้า
- ตอบแชทหรือคำถามจากลูกค้า
- ให้คะแนน/รีวิวลูกค้า

### 👤 ลูกค้า (Customer)
**บทบาท**: ผู้ใช้งานระบบเพื่อนัดหมาย เข้ารับบริการ และดูข้อมูลต่างๆ

**ฟังก์ชันหลัก**:
- ค้นหาร้านในพื้นที่หรือร้านที่มีบริการเฉพาะทาง
- จองคิวล่วงหน้า (เลือกเวลา, ช่าง, บริการ)
- เลือกทรงผมแนะนำจากระบบ AI
- ดูรายการสินค้า/บริการของร้าน
- ตรวจสอบสถานะคิวของตนเองแบบเรียลไทม์
- ชำระเงินออนไลน์ผ่านระบบ
- ดูและให้รีวิวร้าน/ช่าง
- รับการแจ้งเตือนต่างๆ
- จัดการข้อมูลโปรไฟล์ส่วนตัว
