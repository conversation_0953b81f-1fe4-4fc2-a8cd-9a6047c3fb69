# C4 Context Diagram - Hair Salon Management System

```mermaid
C4Context
    title System Context Diagram - Hair Salon Management System

    Person(customer, "Customer", "ลูกค้าที่ต้องการใช้บริการทำผม")
    Person(shopOwner, "Shop Owner", "เจ้าของร้านทำผม ผู้ประกอบการ")
    Person(admin, "System Admin", "ผู้ดูแลระบบและจัดการข้อมูล")
    Person(stylist, "Hair Stylist", "ช่างทำผม ผู้ให้บริการ")

    System(hairSalonSystem, "Hair Salon Management System", "ระบบจัดการร้านทำผมครบวงจร รองรับการจองออนไลน์ AI แนะนำทรงผม และการจัดการธุรกิจ")

    System_Ext(paymentGateway, "Payment Gateway", "ระบบชำระเงินออนไลน์ PromptPay, Credit Card")
    System_Ext(googleMaps, "Google Maps API", "แสดงแผนที่และค้นหาตำแหน่ง")
    System_Ext(cloudStorage, "Cloud Storage", "เก็บข้อมูลภาพและไฟล์ต่างๆ")
    System_Ext(pushNotification, "Push Notification Service", "ส่งการแจ้งเตือนแบบ Push")
    System_Ext(emailService, "Email Service", "ส่งอีเมลยืนยันและแจ้งเตือน")
    System_Ext(smsService, "SMS Service", "ส่ง SMS แจ้งเตือนการจอง")
    System_Ext(socialMedia, "Social Media APIs", "เชื่อมต่อ Facebook, Instagram, Line")
    System_Ext(aiService, "AI/ML Service", "บริการ AI สำหรับแนะนำทรงผมและวิเคราะห์ใบหน้า")

    Rel(customer, hairSalonSystem, "ค้นหาร้าน จองคิว ใช้ AI แนะนำทรงผม ชำระเงิน")
    Rel(shopOwner, hairSalonSystem, "จัดการร้าน ดูรายงาน จัดการลูกค้า และช่างทำผม")
    Rel(admin, hairSalonSystem, "จัดการระบบ ดูแลผู้ใช้ และสำรองข้อมูล")
    Rel(stylist, hairSalonSystem, "ดูตารางงาน เช็คอินลูกค้า อัพเดทสถานะ")

    Rel(hairSalonSystem, paymentGateway, "ประมวลผลการชำระเงิน")
    Rel(hairSalonSystem, googleMaps, "ค้นหาตำแหน่งร้าน แสดงแผนที่")
    Rel(hairSalonSystem, cloudStorage, "เก็บข้อมูลภาพผลงาน โปรไฟล์")
    Rel(hairSalonSystem, pushNotification, "ส่งการแจ้งเตือนการจอง")
    Rel(hairSalonSystem, emailService, "ส่งอีเมลยืนยันการจอง")
    Rel(hairSalonSystem, smsService, "ส่ง SMS แจ้งเตือน")
    Rel(hairSalonSystem, socialMedia, "แชร์ผลงาน เชื่อมต่อโซเชียล")
    Rel(hairSalonSystem, aiService, "วิเคราะห์ใบหน้า แนะนำทรงผม")

    UpdateElementStyle(customer, $fontColor="white", $bgColor="#1976d2")
    UpdateElementStyle(shopOwner, $fontColor="white", $bgColor="#388e3c")
    UpdateElementStyle(admin, $fontColor="white", $bgColor="#f57c00")
    UpdateElementStyle(stylist, $fontColor="white", $bgColor="#7b1fa2")
    UpdateElementStyle(hairSalonSystem, $fontColor="white", $bgColor="#d32f2f")
```

## Key Components Description

### Internal Users
- **Customer (ลูกค้า)**: End users who book appointments, receive AI hair recommendations, and use salon services
- **Shop Owner (เจ้าของร้าน)**: Business owners who manage their salon operations, view analytics, and oversee staff
- **System Admin (ผู้ดูแลระบบ)**: Technical administrators who maintain the system and manage user accounts
- **Hair Stylist (ช่างทำผม)**: Service providers who manage their schedules and serve customers

### Core System
- **Hair Salon Management System**: The main platform providing comprehensive salon management including online booking, AI recommendations, and business analytics

### External Systems
- **Payment Gateway**: Handles secure online payments via PromptPay and credit cards
- **Google Maps API**: Provides location services and mapping functionality
- **Cloud Storage**: Stores images, portfolios, and other media files
- **Push Notification Service**: Sends real-time notifications to mobile apps
- **Email Service**: Handles email confirmations and notifications
- **SMS Service**: Sends text message alerts and confirmations
- **Social Media APIs**: Integrates with Facebook, Instagram, and Line for marketing
- **AI/ML Service**: Provides facial analysis and hair style recommendations

## System Interactions

The system facilitates seamless interactions between all stakeholders while integrating with external services to provide a comprehensive salon management experience. Customers can discover salons, book appointments, receive personalized recommendations, and make payments, while salon owners can manage their business operations and staff efficiently.
