<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Use Case Diagram - Hair Salon Management System</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 10px;
        }
        
        .header h1 {
            color: #1976d2;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 14px;
        }
        
        .diagram-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
            font-size: 12px;
        }
        
        .summary-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        
        .summary-section h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        
        .use-case-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .use-case-list li {
            padding: 3px 0;
            border-bottom: 1px dotted #eee;
        }
        
        .use-case-list li:last-child {
            border-bottom: none;
        }
        
        .admin { color: #f57c00; }
        .staff { color: #388e3c; }
        .customer { color: #1976d2; }
        .shared { color: #7b1fa2; }
        .external { color: #d32f2f; }
        
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Hair Salon Management System</h1>
        <p>Use Case Diagram - ระบบจัดการร้านทำผมครบวงจร</p>
    </div>

    <div class="diagram-container">
        <div class="mermaid">
graph LR
    %% Actors
    Admin[🔧 Admin]
    Staff[🧑‍🔧 Staff]
    Customer[👤 Customer]
    
    %% System Boundary
    subgraph System["🎯 Hair Salon Management System"]
        %% Admin Use Cases
        subgraph AdminUC["📊 Admin"]
            A1[จัดการร้าน]
            A2[จัดการผู้ใช้]
            A3[ดูรายงาน]
            A4[สำรองข้อมูล]
        end
        
        %% Staff Use Cases
        subgraph StaffUC["💼 Staff"]
            S1[จัดการบริการ]
            S2[จัดการคิว]
            S3[ดูข้อมูลลูกค้า]
            S4[Face Recognition]
            S5[แชทลูกค้า]
        end
        
        %% Customer Use Cases
        subgraph CustomerUC["✨ Customer"]
            C1[ค้นหาร้าน]
            C2[จองคิว]
            C3[AI แนะนำ]
            C4[ชำระเงิน]
            C5[ตรวจสอบคิว]
            C6[รีวิว]
        end
        
        %% Shared
        Login[🔑 Login]
    end
    
    %% External Systems
    Payment[💳 Payment]
    Maps[🗺️ Maps]
    AI[🤖 AI Engine]
    
    %% Relationships
    Admin --> A1
    Admin --> A2
    Admin --> A3
    Admin --> A4
    Admin --> Login
    
    Staff --> S1
    Staff --> S2
    Staff --> S3
    Staff --> S4
    Staff --> S5
    Staff --> Login
    
    Customer --> C1
    Customer --> C2
    Customer --> C3
    Customer --> C4
    Customer --> C5
    Customer --> C6
    Customer --> Login
    
    %% External connections
    C4 -.-> Payment
    C1 -.-> Maps
    S4 -.-> AI
    C3 -.-> AI
    
    %% Include/Extend
    C2 -.->|includes| Login
    S2 -.->|includes| S4
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef admin fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef staff fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef customer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef shared fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    
    class Admin,Staff,Customer actor
    class A1,A2,A3,A4 admin
    class S1,S2,S3,S4,S5 staff
    class C1,C2,C3,C4,C5,C6 customer
    class Login shared
    class Payment,Maps,AI external
        </div>
    </div>

    <div class="summary">
        <div class="summary-section">
            <h3 class="admin">📊 Admin Functions</h3>
            <ul class="use-case-list">
                <li><strong>A1:</strong> จัดการร้าน - เพิ่ม/แก้ไข/ลบ ข้อมูลร้าน</li>
                <li><strong>A2:</strong> จัดการผู้ใช้ - จัดการบัญชีและสิทธิ์</li>
                <li><strong>A3:</strong> ดูรายงาน - รายงานยอดขาย/สถิติ</li>
                <li><strong>A4:</strong> สำรองข้อมูล - Backup/Restore ระบบ</li>
            </ul>
            
            <h3 class="staff">💼 Staff Functions</h3>
            <ul class="use-case-list">
                <li><strong>S1:</strong> จัดการบริการ - เพิ่ม/แก้ไข บริการและโปรโมชั่น</li>
                <li><strong>S2:</strong> จัดการคิว - ยืนยัน/ยกเลิก/แก้ไข การนัดหมาย</li>
                <li><strong>S3:</strong> ดูข้อมูลลูกค้า - ประวัติ/ความชอบ/สต็อก</li>
                <li><strong>S4:</strong> Face Recognition - ตรวจสอบตัวตน/เช็กอิน</li>
                <li><strong>S5:</strong> แชทลูกค้า - ตอบคำถาม/ให้คะแนน</li>
            </ul>
        </div>
        
        <div class="summary-section">
            <h3 class="customer">✨ Customer Functions</h3>
            <ul class="use-case-list">
                <li><strong>C1:</strong> ค้นหาร้าน - ค้นหาตามพื้นที่/บริการ</li>
                <li><strong>C2:</strong> จองคิว - เลือกเวลา/ช่าง/บริการ</li>
                <li><strong>C3:</strong> AI แนะนำ - แนะนำทรงผมด้วย AI</li>
                <li><strong>C4:</strong> ชำระเงิน - QR/PromptPay/บัตรเครดิต</li>
                <li><strong>C5:</strong> ตรวจสอบคิว - สถานะคิวเรียลไทม์</li>
                <li><strong>C6:</strong> รีวิว - ให้คะแนน/รีวิว/จัดการโปรไฟล์</li>
            </ul>
            
            <h3 class="shared">🔑 Shared Function</h3>
            <ul class="use-case-list">
                <li><strong>Login:</strong> เข้าสู่ระบบ - Authentication ทุกผู้ใช้</li>
            </ul>
            
            <h3 class="external">🔗 External Systems</h3>
            <ul class="use-case-list">
                <li><strong>Payment:</strong> ระบบชำระเงิน</li>
                <li><strong>Maps:</strong> Google Maps API</li>
                <li><strong>AI Engine:</strong> ระบบ AI แนะนำทรงผม</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
